import CoreDataManager from '../../native/CoreDataManager';
import { Song } from "../../types";

export async function loadHistory(): Promise<Song[]> {
    return await CoreDataManager.getSongs();
}

export async function addToHistory(song: Song) {
    return await CoreDataManager.addSong(song);
}

export async function removeFromHistory(song: Song) {
    return await CoreDataManager.removeSong(song);
}