/**
 * If you are not familiar with React Navigation, refer to the "Fundamentals" guide:
 * https://reactnavigation.org/docs/getting-started
 *
 */
import Ionicons from 'react-native-ionicons';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationContainer, DefaultTheme, DarkTheme } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import * as React from 'react';
import { ColorSchemeName, Platform, Pressable, Text } from 'react-native';

import Colors from '../constants/Colors';
import useColorScheme from '../hooks/useColorScheme';
import InfoModalScreen from '../screens/InfoModalScreen';
import SearchScreen from '../screens/SearchScreen';
import HistoryScreen from '../screens/HistoryScreen';
import { RootStackParamList, RootTabParamList, RootTabScreenProps } from '../types';
import SongDetailScreen from '../screens/SongDetailScreen';

export default function Navigation({ colorScheme }: { colorScheme: ColorSchemeName }) {
  return (
    <NavigationContainer
      theme={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <RootNavigator />
    </NavigationContainer>
  );
}

/**
 * A root stack navigator is often used for displaying modals on top of all other content.
 * https://reactnavigation.org/docs/modal
 */
const Stack = createNativeStackNavigator<RootStackParamList>();

function RootNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Root" component={BottomTabNavigator} options={{ headerShown: false }} />
      <Stack.Group screenOptions={{ presentation: 'modal' }}>
        <Stack.Screen name="InfoModal" component={InfoModalScreen} options={{
          headerShown: false
        }} />
      </Stack.Group>
    </Stack.Navigator>
  );
}

function SearchDetailNavigator() {
  const colorScheme = useColorScheme();

  return (
    <Stack.Navigator>
      <Stack.Screen name="Search" component={SearchScreen} options={({ navigation }) => ({
        title: 'Search',
        headerRight: ({ tintColor }) => {
          const color = tintColor ? tintColor : colorScheme === 'light' ? 'black' : 'white';
          
          return (
            <Pressable
              onPress={() => navigation.navigate('InfoModal')}
              style={({ pressed }) => ({
                opacity: pressed ? 0.5 : 1,
              })}>
              <Ionicons name="help-circle-outline" size={24} color={color} style={{ marginEnd: (Platform.OS === 'web' ? 16 : 0) }} />
            </Pressable>
          );
        }
      })} />
      <Stack.Screen name="SongDetail" component={SongDetailScreen} options={{ title: "Song Detail" }} />
    </Stack.Navigator>
  )
}

function HistoryDetailNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen name="History" component={HistoryScreen} />
      <Stack.Screen name="SongDetail" component={SongDetailScreen} options={{ title: "Song Detail" }} />
    </Stack.Navigator>
  )
}

const BottomTab = createBottomTabNavigator<RootTabParamList>();

function BottomTabNavigator() {
  const colorScheme = useColorScheme();

  return (
    <BottomTab.Navigator
      initialRouteName="SearchTab"
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme].tint,
      }}>
      <BottomTab.Screen
        name="SearchTab"
        component={SearchDetailNavigator}
        options={{
          headerShown: false,
          tabBarIcon: ({ color }) => <TabBarIcon name="search" color={color} />,
          tabBarLabel: "Search",
        }}
      />
      <BottomTab.Screen
        name="HistoryTab"
        component={HistoryDetailNavigator}
        options={{
          headerShown: false,
          tabBarIcon: ({ color }) => <TabBarIcon name="time" color={color} />,
          tabBarLabel: "History",
        }}
      />
    </BottomTab.Navigator>
  );
}

/**
 * You can explore the built-in icon families and icons on the web at https://icons.expo.fyi/
 */
function TabBarIcon(props: {
  name: React.ComponentProps<typeof Ionicons>['name'];
  color: string;
}) {
  return <Ionicons size={30} style={{ marginBottom: -3 }} {...props} />;
}
