{"$schema": "https://json.schemastore.org/tsconfig", "display": "React Native", "compilerOptions": {"target": "esnext", "module": "commonjs", "lib": ["es2017"], "allowJs": true, "jsx": "react-native", "noEmit": true, "isolatedModules": true, "strict": true, "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": false}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "expo/tsconfig.base"}