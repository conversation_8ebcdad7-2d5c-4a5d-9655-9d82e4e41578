import Clipboard from '@react-native-clipboard/clipboard';
import { useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { validateURL } from '../helpers/URLValidator';

export default function useValidClipboard(): boolean {
    const [validClipboard, setValidClipboard] = useState(false);

    useEffect(() => {
        const intervalId = setInterval(() => {
            (async () => {
                switch (Platform.OS) {
                    case 'ios': {
                        setValidClipboard(await Clipboard.hasWebURL());

                        break;
                    }

                    case 'android': {
                        const content = await Clipboard.getString();

                        setValidClipboard(validateURL(content));

                        break;
                    }

                    default:
                        break;
                }
            })();
        }, 1000);

        return () => clearInterval(intervalId);
    }, []);

    return validClipboard;
}