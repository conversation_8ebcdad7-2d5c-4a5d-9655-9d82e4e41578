import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';

import { store } from './store'

import useColorScheme from './hooks/useColorScheme';
import Navigation from './navigation';
import StorageLoader from './helpers/StorageLoader';
import { StatusBar } from 'react-native';

export default function App() {
  const colorScheme = useColorScheme();

  return (
    <Provider store={store}>
      <StorageLoader>
        <SafeAreaProvider>
          <StatusBar backgroundColor={'#000000'} hidden={false} />
          <Navigation colorScheme={colorScheme} />
        </SafeAreaProvider>
      </StorageLoader>
    </Provider>
  );
}
