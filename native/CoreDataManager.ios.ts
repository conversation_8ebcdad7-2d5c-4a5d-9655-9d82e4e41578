import { NativeModules } from "react-native";
import { Song } from "../types";

const { CoreDataManager } = NativeModules;

async function getSongs(): Promise<Song[]> {
    let songs = await CoreDataManager.getSongs("");

    return JSON.parse(songs);
}

async function addSong(song: Song) {
    let data = JSON.stringify(song);

    await CoreDataManager.addSong(data);
}

async function removeSong(song: Song) {
    let data = JSON.stringify(song);

    await CoreDataManager.removeSong(data);
}

export default { getSongs, addSong, removeSong };