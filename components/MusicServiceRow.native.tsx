import React from 'react';
import { GestureResponderEvent, Image, Pressable, StyleSheet } from 'react-native';
import Ionicons from 'react-native-ionicons';

import useColorScheme from '../hooks/useColorScheme';
import { Text, View } from '../components/Themed';
import { MusicServiceRowProps } from '../types';

export default function MusicServiceRow({ musicServiceName, musicServiceImageSource, onPress, style }: MusicServiceRowProps) {

    const colorScheme = useColorScheme();
  
    const shareColorScheme = colorScheme === 'light' ? styles.lightThemeShare : styles.darkThemeShare;
  
    return (
      <View style={style}>
        <Pressable onPress={(event: GestureResponderEvent) => onPress(false)} style={({ pressed }) => pressed ? { ...styles.musicServiceInner, opacity: 0.5 } : styles.musicServiceInner}>
          <Image source={musicServiceImageSource} style={styles.musicService} />
          <View style={styles.spacer} />
          <Text>{musicServiceName}</Text>
        </Pressable>
        <View style={{ flex: 1 }} />
        <Pressable style={({ pressed }) => pressed ? { opacity: 0.5 } : {}} hitSlop={10} pressRetentionOffset={20} onPress={() => { onPress(true) }}>
          <Ionicons name="share" size={26} color={shareColorScheme.color} />
        </Pressable>
      </View>
    )
  }

  const styles = StyleSheet.create({
    musicService: {
      width: 25,
      height: 25,
      aspectRatio: 1,
      resizeMode: 'contain'
    },
    spacer: {
      padding: 5, 
      backgroundColor: 'transparent'
    },
    musicServiceInner: {
      flexDirection: 'row', 
      alignItems: 'center',
      backgroundColor: 'transparent'
    },
    lightThemeShare: {
      color: '#242c40',
    },
    darkThemeShare: {
      color: '#d0d0c0',
    },
  });