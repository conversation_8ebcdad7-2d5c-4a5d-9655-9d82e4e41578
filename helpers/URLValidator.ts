import { URL } from 'react-native-url-polyfill';

function getHost(urlString: string): String {
    const url = new URL(urlString);

    let host = url.host.toLowerCase();

    if (host.startsWith("www."))
        host = host.substring(4);
    
    if (host.startsWith("m."))
        host = host.substring(2);
    
    return host;
}

export function validateURL(urlString: string): boolean {
    try {
        const host = getHost(urlString);

        return (
            host == "youtube.com" || 
            host == "youtu.be" || 
            host == "music.apple.com" || 
            host == "open.spotify.com"
        );
    } catch (e) {
        return false;
    }
}