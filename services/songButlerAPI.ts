import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { APISongResponse } from '../types';

import * as ClientType from '../helpers/ClientType';

type APIResponseSuccess = {
    success: true;
    data: APISongResponse;
}

const defaultHeaders = {
    'X-Client-Type': ClientType.get()
};

export const songButlerAPI = createApi({
    reducerPath: 'songButlerAPI',
    baseQuery: fetchBaseQuery({ baseUrl: 'https://api.songbutler.app/api/' }),
    endpoints: (builder) => ({
        getTrackDataFromQuery: builder.mutation<APISongResponse, string>({
            query: (body) => ({
                url: 'search',
                method: 'POST',
                headers: defaultHeaders,
                body
            }),
            transformResponse: (response: APIResponseSuccess) => response.data,
        }),
        getTrackDataFromURL: builder.mutation<APISongResponse, string>({
            query: (body) => ({
                url: 'link',
                method: 'POST',
                headers: defaultHeaders,
                body
            }),
            transformResponse: (response: APIResponseSuccess) => response.data
        })
    })
});

export const { useGetTrackDataFromQueryMutation, useGetTrackDataFromURLMutation } = songButlerAPI;