{"name": "songbutlerrn", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@edualm/react-native-now-playing": "^0.2.1", "@edualm/react-native-shazam-kit": "^0.1.3", "@expo/cli": "^54.0.1", "@react-native-async-storage/async-storage": "^1.17.3", "@react-native-clipboard/clipboard": "^1.8.5", "@react-navigation/bottom-tabs": "^6.0.5", "@react-navigation/native": "^6.0.2", "@react-navigation/native-stack": "^6.1.0", "@reduxjs/toolkit": "^1.7.2", "moment": "^2.29.1", "react": "17.0.1", "react-dom": "17.0.1", "react-native": "0.64.3", "react-native-ionicons": "^4.6.5", "react-native-safe-area-context": "3.3.2", "react-native-screens": "~3.10.1", "react-native-share": "^7.3.7", "react-native-url-polyfill": "^1.3.0", "react-native-web": "^0.17.7", "react-redux": "^7.2.6", "realm": "^10.12.0", "redux-thunk": "^2.4.1"}, "devDependencies": {"@babel/core": "^7.12.9", "@types/react": "^18.0.1", "@types/react-native": "~0.64.12", "jest": "^26.6.3", "jest-expo": "~44.0.1", "react-test-renderer": "17.0.1", "typescript": "~4.3.5"}, "private": true}