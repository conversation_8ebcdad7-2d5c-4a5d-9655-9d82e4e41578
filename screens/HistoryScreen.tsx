import React, { useCallback, useMemo, useState } from 'react';
import { Image, FlatList, StyleSheet, Pressable, TextInput } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

import moment from 'moment';

import { Text, View } from '../components/Themed';
import { setDetail } from '../features/detail/detailSlice';
import { RootState } from '../store';
import { RootTabScreenProps, Song } from '../types';
import Ionicons from 'react-native-ionicons';
import useColorScheme from '../hooks/useColorScheme';
import { albumArtSourceOrDefault } from '../helpers/AlbumArtURL';

const ItemDivider = () => (
  <View style={styles.separator} />
);

const SearchBar = (props: { query: string, onChangeQuery: (query: string) => void}) => (
  <View style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: 'gray', borderRadius: 25, margin: 10 }}>
    <Ionicons name="search" size={18} color="black" style={{ margin: 8, marginLeft: 12 }} />
    <TextInput 
      autoCapitalize='none' 
      placeholder='Search...' 
      editable={true} 
      style={{ flex: 1, height: 40, marginRight: 8, alignSelf: 'stretch' }} 
      maxLength={40} 
      value={props.query} 
      onChangeText={props.onChangeQuery} 
      clearButtonMode='always'
    />
  </View>
);

export default function HistoryScreen({ navigation }: RootTabScreenProps<'HistoryTab'>) {

  const colorScheme = useColorScheme();

  const [query, setQuery] = useState('');

  const dispatch = useDispatch();

  const data = useSelector((state: RootState) => state.history.entries);

  const onHistoryPress = (item: Song) => {
    dispatch(setDetail(item));

    navigation.navigate('SongDetail');
  };

  const filteredData = useMemo(() => {
    return data.slice().reverse().filter(({ album, artist, title }) => {
      if (query === '')
        return true;
      
      let lowercasedQuery = query.toLowerCase();
      
      if (album && album.toLowerCase().indexOf(lowercasedQuery) !== -1)
        return true;

      if (artist && artist.toLowerCase().indexOf(lowercasedQuery) !== -1)
        return true;

      if (title && title.toLowerCase().indexOf(lowercasedQuery) !== -1)
        return true;
      
      return false;
    })
  }, [data, query]);

  const renderItem = useCallback((item: Song, colorScheme: string) => (
    <Pressable style={({pressed}) => {return pressed ? (colorScheme == 'dark' ? styles.rowPressedDark : styles.rowPressedLight) : styles.row}} onPress={() => onHistoryPress(item)}>
      <View style={{ paddingEnd: 10, backgroundColor: 'transparent' }}>
        <Image style={styles.albumArt} source={albumArtSourceOrDefault(item.albumArtURL)} />
      </View>
      <View style={{ flex: 1, justifyContent: 'center', backgroundColor: 'transparent' }}>
        <Text style={styles.rowTitle} numberOfLines={1}>{item.title}</Text>
        <View style={{ flexDirection: 'row', alignItems: 'flex-end', backgroundColor: 'transparent' }}>
          <Ionicons name="disc" size={12} color={colorScheme === 'light' ? 'black' : 'white'} style={{ marginEnd: 6 }} />
          <Text style={styles.rowSubtitleBold} numberOfLines={1}>{item.artist} • {item.album}</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'flex-end', backgroundColor: 'transparent' }}>
          <Ionicons name="calendar" size={12} color={colorScheme === 'light' ? 'black' : 'white'} style={{ marginEnd: 6 }} />
          <Text style={styles.rowSubtitle} numberOfLines={1}>Last search: {moment(item.lastQueryDate).format('LLL')}</Text>
        </View>
      </View>
    </Pressable>
  ), []);

  return (
    <View style={{ flex: 1 }}>
      <SearchBar
        query={query} 
        onChangeQuery={setQuery}
      />
      <FlatList 
        data={filteredData} 
        contentContainerStyle={{ flexGrow: 1 }}
        renderItem={(x) => renderItem(x.item, colorScheme)} 
        keyExtractor={item => JSON.stringify(item.queryDates)} 
        ItemSeparatorComponent={ItemDivider}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  albumArt: {
    width: 60,
    height: 60,
    borderRadius: 10,
    borderColor: 'gray',
    borderWidth: 2
  },
  row: {
    padding: 16,
    flex: 1,
    flexDirection: 'row'
  },
  rowPressedLight: {
    padding: 16,
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#d3d3d3'
  },
  rowPressedDark: {
    padding: 16,
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#5b5b5b'
  },
  rowTitle: {
    fontSize: 22,
    fontWeight: 'bold'
  },
  rowSubtitleBold: {
    fontSize: 12,
    fontWeight: 'bold'
  },
  rowSubtitle: {
    fontSize: 12
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  separator: {
    backgroundColor: "#939393",
    height: 1,
    width: '100%',
    marginLeft: 16
  },
});
