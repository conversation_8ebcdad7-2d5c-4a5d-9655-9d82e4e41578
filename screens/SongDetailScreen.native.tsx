import React, { memo, useCallback } from 'react';
import { Alert, GestureResponderEvent, Image, Linking, Pressable, ScrollView, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Ionicons from 'react-native-ionicons';
import Share from 'react-native-share';

import useColorScheme from '../hooks/useColorScheme';
import { Text, View } from '../components/Themed';
import { RootState } from '../store';
import MusicServiceRow from '../components/MusicServiceRow';
import { deleteFromHistory } from '../features/history/historySlice';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types';
import { albumArtSourceOrDefault } from '../helpers/AlbumArtURL';

import moment from 'moment';

const onServicePress = (url: string | undefined, share: boolean) => {
  if (url == null)
    return true;
  
  if (share) {
    Share.open({ message: url });
  } else {
    Linking.openURL(url);
  }

  return true;
};

const SongDetailScreen = ({ navigation }: NativeStackScreenProps<RootStackParamList>) => {

  const colorScheme = useColorScheme();

  const shareColorScheme = colorScheme === 'light' ? styles.lightThemeShare : styles.darkThemeShare;

  const historyEntry = useSelector((state: RootState) => state.detail.data);

  const dispatch = useDispatch();
  
  const onPressAppleMusic = useCallback((share: boolean) => {
    return onServicePress(historyEntry?.links.appleMusic, share);
  }, [historyEntry]);

  const onPressSpotify = useCallback((share: boolean) => {
    return onServicePress(historyEntry?.links.spotify, share);
  }, [historyEntry]);

  const onPressYouTube = useCallback((share: boolean) => {
    return onServicePress(historyEntry?.links.youtube, share);
  }, [historyEntry]);

  const onPressDelete = useCallback((event: GestureResponderEvent) => {
    if (historyEntry == null)
      return true;
    
    Alert.alert("Confirm Deletion", "Are you sure you want to delete this item from your history?", 
    [
      { 
        text: 'Delete', 
        style: 'destructive', 
        onPress: () => {
          dispatch(deleteFromHistory(historyEntry));

          navigation.goBack();
        } 
      },
      { 
        text: 'Cancel', 
        style: 'cancel', 
        onPress: () => {} 
      }
    ]);

    return true;
  }, [historyEntry]);

  const onPressShareAll = useCallback((event: GestureResponderEvent) => {
    if (historyEntry == null)
      return true;

    const shareString = `${historyEntry.title} by ${historyEntry.artist} (${historyEntry.album})

Apple Music: ${historyEntry.links.appleMusic ?? '(not found)'}
Spotify: ${historyEntry.links.spotify ?? '(not found)'}
YouTube: ${historyEntry.links.youtube ?? '(not found)'}

(Shared with SongButler - https://songbutler.app/)`
    
    Share.open({ message: shareString });

    return true;
  }, [historyEntry]);

  if (historyEntry == null)
    return (
      <Text>Error loading data.</Text>
    );

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Image style={styles.albumArt} source={albumArtSourceOrDefault(historyEntry?.albumArtURL)} />
      <Text style={styles.title}>{historyEntry.title}</Text>
      <View style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: 'transparent' }}>
        <Ionicons name="person" size={16} style={{ paddingEnd: 8 }} color={colorScheme === 'light' ? 'black' : 'white'} />
        <Text style={styles.subtitle}>{historyEntry.artist}</Text>
      </View>
      <View style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: 'transparent', marginTop: 8 }}>
        <Ionicons name="disc" size={16} style={{ paddingEnd: 8 }} color={colorScheme === 'light' ? 'black' : 'white'} />
        <Text style={styles.subtitle}>{historyEntry.album}</Text>
      </View>

      <View style={styles.separator} />

      <MusicServiceRow musicServiceName='Apple Music' musicServiceImageSource={require('../assets/images/apple_music.png')} onPress={onPressAppleMusic} style={styles.shareRow} />
      <MusicServiceRow musicServiceName='Spotify' musicServiceImageSource={require('../assets/images/spotify.png')} onPress={onPressSpotify} style={styles.shareRow} />
      <MusicServiceRow musicServiceName='YouTube' musicServiceImageSource={require('../assets/images/youtube.png')} onPress={onPressYouTube} style={styles.shareRow} />

      <Pressable hitSlop={10} pressRetentionOffset={20} style={({ pressed }) => pressed ? { ...styles.shareAllRow, opacity: 0.5 } : styles.shareAllRow} onPress={onPressShareAll}>
        <Ionicons name="share" size={26} color={shareColorScheme.color} style={{ paddingEnd: 8 }} />
        <Text style={{ fontSize: 16 }}>Share All</Text>
      </Pressable>

      <View style={styles.separator} />

      <View style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: 'transparent', marginBottom: 16 }}>
        <Ionicons name="pricetag" size={16} style={{ paddingEnd: 8 }} color={colorScheme === 'light' ? 'black' : 'white'} />
        <Text style={styles.subtitle}>Times searched: {historyEntry.queryDates.length}</Text>
      </View>

      {historyEntry.queryDates.map(date => 
        <View style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: 'transparent' }} key={date}>
          <Ionicons name="calendar" size={16} style={{ paddingEnd: 8 }} color={colorScheme === 'light' ? 'black' : 'white'} />
          <Text key={date} >{moment(date).format('LLL')}</Text>
        </View>
      )}

      <View style={styles.separator} />

      <Pressable hitSlop={10} pressRetentionOffset={20} style={({ pressed }) => pressed ? { ...styles.deleteRow, opacity: 0.5 } : styles.deleteRow} onPress={onPressDelete}>
        <Ionicons name="trash" size={26} color='red' style={{ paddingEnd: 8 }} />
        <Text style={{ color: 'red', fontSize: 16 }}>Delete</Text>
      </Pressable>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  albumArt: {
    width: 250,
    height: 250,
    borderRadius: 10,
    borderColor: 'gray',
    borderWidth: 3,
    paddingBottom: 10
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 25
  },
  containerWeb: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 25,
    width: '50%'
  },
  musicService: {
    width: 25,
    height: 25,
    aspectRatio: 1,
    resizeMode: 'contain'
  },
  title: {
    fontSize: 32,
    paddingTop: 10,
    paddingBottom: 10,
    textAlign: 'center'
  },
  subtitle: {
    textAlign: 'center'
  },
  shareAllRow: {
    flexDirection: 'row', 
    alignItems: 'center',
    backgroundColor: 'transparent'
  },
  deleteRow: {
    flexDirection: 'row', 
    alignItems: 'center',
    backgroundColor: 'transparent'
  },
  separator: {
    marginVertical: 30,
    height: 1,
    width: '90%',
    backgroundColor: 'gray'
  },
  lightThemeShare: {
    color: '#242c40',
  },
  darkThemeShare: {
    color: '#d0d0c0',
  },
  shareRow: {
    flexDirection: 'row', 
    alignItems: 'center', 
    paddingBottom: 25,
    backgroundColor: 'transparent'
  }
});

export default memo(SongDetailScreen);