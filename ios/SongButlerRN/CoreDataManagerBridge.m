//
//  CoreDataManagerBridge.m
//  SongButlerRN
//
//  Created by <PERSON> on 12/04/2022.
//

#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(CoreDataManager, NSObject)

RCT_EXTERN_METHOD(addSong:(NSString *)songData
                  withResolver:(RCTPromiseResolveBlock)resolve
                  withRejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(removeSong:(NSString *)songData
                  withResolver:(RCTPromiseResolveBlock)resolve
                  withRejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(getSongs:(NSString *)ignored
                  withResolver:(RCTPromiseResolveBlock)resolve
                  withRejecter:(RCTPromiseRejectBlock)reject)

@end
