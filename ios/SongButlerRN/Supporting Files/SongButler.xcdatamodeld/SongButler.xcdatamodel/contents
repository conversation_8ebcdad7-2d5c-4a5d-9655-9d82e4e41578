<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="20086" systemVersion="21E258" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="YES" userDefinedModelVersionIdentifier="">
    <entity name="Song" representedClassName=".Song" syncable="YES" codeGenerationType="class">
        <attribute name="album" optional="YES" attributeType="String" spotlightIndexingEnabled="YES"/>
        <attribute name="albumArtURL" optional="YES" attributeType="String"/>
        <attribute name="artist" optional="YES" attributeType="String" spotlightIndexingEnabled="YES"/>
        <attribute name="lastQueryDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="queryDates" optional="YES" attributeType="Transformable" valueTransformerName="" customClassName="[Date]"/>
        <attribute name="title" optional="YES" attributeType="String" spotlightIndexingEnabled="YES"/>
        <relationship name="songLinks" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="SongLinks" inverseName="song" inverseEntity="SongLinks"/>
    </entity>
    <entity name="SongLinks" representedClassName=".SongLinks" syncable="YES" codeGenerationType="class">
        <attribute name="appleMusic" optional="YES" attributeType="String"/>
        <attribute name="spotify" optional="YES" attributeType="String"/>
        <attribute name="youtube" optional="YES" attributeType="String"/>
        <relationship name="song" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Song" inverseName="songLinks" inverseEntity="Song"/>
    </entity>
    <elements>
        <element name="Song" positionX="-63" positionY="-18" width="128" height="134"/>
        <element name="SongLinks" positionX="193.640625" positionY="-3.3984375" width="128" height="89"/>
    </elements>
</model>