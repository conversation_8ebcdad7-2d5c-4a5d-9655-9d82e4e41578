//
//  Song+CoreData.swift
//  SongButlerRN
//
//  Created by <PERSON> on 12/04/2022.
//

import CoreData
import Foundation
import UIKit

extension Song {
  
  static func fetchAll() -> [Song] {
    return fetchAll(viewContext: (UIApplication.shared.delegate as! AppDelegate).persistentContainer.viewContext)
  }
  
  static func fetchAll(viewContext: NSManagedObjectContext) -> [Song] {
    let request: NSFetchRequest<Song> = Song.fetchRequest();
    
    request.sortDescriptors = [NSSortDescriptor(key: "lastQueryDate", ascending: true)]
    
    guard let songs = try? viewContext.fetch(request) else {
      return []
    }
    
    return songs
  }
  
  static func create(bridgedSong: BridgedSong, viewContext: NSManagedObjectContext) -> Song {
    let songLinks = SongLinks(context: viewContext)
    
    songLinks.appleMusic = bridgedSong.links.appleMusic
    songLinks.spotify = bridgedSong.links.spotify
    songLinks.youtube = bridgedSong.links.youtube
    
    let song = Song(context: viewContext)
    
    song.album = bridgedSong.album
    song.artist = bridgedSong.artist
    song.title = bridgedSong.title
    
    song.albumArtURL = bridgedSong.albumArtURL
    
    song.songLinks = songLinks
    
    song.queryDates = bridgedSong.queryDates
    song.lastQueryDate = bridgedSong.lastQueryDate
    
    try! viewContext.save()
    
    return song
  }
}
