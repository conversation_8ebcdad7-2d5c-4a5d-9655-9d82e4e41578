//
//  CoreDataManager.swift
//  SongButlerRN
//
//  Created by <PERSON> on 12/04/2022.
//

import Foundation

@objc(CoreDataManager)
class CoreDataManager: NSObject {
  
  @objc(methodQueue)
  var methodQueue: DispatchQueue {
    DispatchQueue.main
  }
  
  @objc(requiresMainQueueSetup)
  static var requiresMainQueueSetup: Bool {
    true
  }
  
  var viewContext: NSManagedObjectContext {
    (UIApplication.shared.delegate as! AppDelegate).persistentContainer.viewContext
  }
  
  lazy var jsonDecoder: JSONDecoder = {
    let decoder = JSONDecoder()
    decoder.dateDecodingStrategy = .millisecondsSince1970
    
    return decoder
  }()
  
  lazy var jsonEncoder: JSONEncoder = {
    let encoder = JSONEncoder()
    encoder.dateEncodingStrategy = .millisecondsSince1970
    
    return encoder
  }()
  
  @objc(addSong:withResolver:withRejecter:)
  func addSong(_ songData: String, resolve: RCTPromiseResolveBlock, reject: RCTPromiseRejectBlock) {
    do {
      let data = try jsonDecoder.decode(BridgedSong.self, from: Data(songData.utf8))
      
      let songs = Song.fetchAll(viewContext: viewContext)
      
      for s in songs {
        if s.isSimilarTo(data) {
          s.lastQueryDate = data.lastQueryDate
          s.queryDates = (data.queryDates ?? []) + [data.lastQueryDate].compactMap { $0 }
          
          try! viewContext.save()
          
          resolve(nil)
          
          return
        }
      }
      
      _ = Song.create(bridgedSong: data, viewContext: viewContext)
      
      resolve(nil)
    } catch {
      reject(nil, nil, error)
    }
  }
  
  @objc(getSongs:withResolver:withRejecter:)
  func getSongs(_ ignored: String, resolve: RCTPromiseResolveBlock, reject: RCTPromiseRejectBlock) {
    let songs = Song.fetchAll(viewContext: viewContext)
    
    let encodedData = try! jsonEncoder.encode(songs.map(BridgedSong.init))
    let json = String(data: encodedData, encoding: .utf8)
    
    resolve(json)
  }
  
  @objc(removeSong:withResolver:withRejecter:)
  func removeSong(_ songData: String, resolve: RCTPromiseResolveBlock, reject: RCTPromiseRejectBlock) {
    let songs = Song.fetchAll(viewContext: viewContext)
    
    let song = try! jsonDecoder.decode(BridgedSong.self, from: Data(songData.utf8))
    
    songs.forEach {
      if $0.isSimilarTo(song) {
        viewContext.delete($0)
      }
    }
    
    try! viewContext.save()
    
    resolve(nil);
  }
}

extension Song {
  
  func isSimilarTo(_ bridgedSong: BridgedSong) -> Bool {
    return artist == bridgedSong.artist && album == bridgedSong.album && title == bridgedSong.title
  }
}

extension BridgedSong {
  
  func isSimilarTo(_ song: Song) -> Bool {
    return artist == song.artist && album == song.album && title == song.title
  }
}
