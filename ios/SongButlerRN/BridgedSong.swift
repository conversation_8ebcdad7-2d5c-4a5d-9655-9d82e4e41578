//
//  BridgedSong.swift
//  SongButlerRN
//
//  Created by <PERSON> on 12/04/2022.
//

import Foundation

struct BridgedSong: Codable {
  
  struct SongLinks: Codable {
    
    let appleMusic: String?
    let spotify: String?
    let youtube: String?
  }
  
  let links: SongLinks
  
  let album: String?
  let albumArtURL: String?
  let artist: String?
  let queryDates: [Date]?
  let lastQueryDate: Date?
  let title: String?
}

extension BridgedSong {
  
  init(_ song: Song) {
    self.links = .init(song.songLinks)
    
    self.album = song.album
    self.albumArtURL = song.albumArtURL
    self.artist = song.artist
    self.queryDates = song.queryDates
    self.lastQueryDate = song.lastQueryDate
    self.title = song.title
  }
}

extension BridgedSong.SongLinks {
  
  init(_ songLinks: SongLinks?) {
    guard let songLinks = songLinks else {
      self.appleMusic = nil
      self.spotify = nil
      self.youtube = nil
      
      return
    }
    
    self.appleMusic = songLinks.appleMusic
    self.spotify = songLinks.spotify
    self.youtube = songLinks.youtube
  }
}
