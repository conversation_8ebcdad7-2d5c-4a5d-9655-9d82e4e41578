PODS:
  - boost-for-react-native (1.63.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.64.3)
  - FBReactNativeSpec (0.64.3):
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.3)
    - RCTTypeSafety (= 0.64.3)
    - React-Core (= 0.64.3)
    - React-jsi (= 0.64.3)
    - ReactCommon/turbomodule/core (= 0.64.3)
  - GCDWebServer (3.5.4):
    - GCDWebServer/Core (= 3.5.4)
  - GCDWebServer/Core (3.5.4)
  - glog (0.3.5)
  - RCT-Folly (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
    - RCT-Folly/Default (= 2020.01.13.00)
  - RCT-Folly/Default (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
  - RCTRequired (0.64.3)
  - RCTTypeSafety (0.64.3):
    - FBLazyVector (= 0.64.3)
    - RCT-<PERSON>olly (= 2020.01.13.00)
    - RCTRequired (= 0.64.3)
    - React-Core (= 0.64.3)
  - React (0.64.3):
    - React-Core (= 0.64.3)
    - React-Core/DevSupport (= 0.64.3)
    - React-Core/RCTWebSocket (= 0.64.3)
    - React-RCTActionSheet (= 0.64.3)
    - React-RCTAnimation (= 0.64.3)
    - React-RCTBlob (= 0.64.3)
    - React-RCTImage (= 0.64.3)
    - React-RCTLinking (= 0.64.3)
    - React-RCTNetwork (= 0.64.3)
    - React-RCTSettings (= 0.64.3)
    - React-RCTText (= 0.64.3)
    - React-RCTVibration (= 0.64.3)
  - React-callinvoker (0.64.3)
  - React-Core (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.3)
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/CoreModulesHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/Default (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/DevSupport (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.3)
    - React-Core/RCTWebSocket (= 0.64.3)
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-jsinspector (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTBlobHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTImageHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTTextHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-Core/RCTWebSocket (0.64.3):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.3)
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsiexecutor (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - Yoga
  - React-CoreModules (0.64.3):
    - FBReactNativeSpec (= 0.64.3)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.3)
    - React-Core/CoreModulesHeaders (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-RCTImage (= 0.64.3)
    - ReactCommon/turbomodule/core (= 0.64.3)
  - React-cxxreact (0.64.3):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-jsinspector (= 0.64.3)
    - React-perflogger (= 0.64.3)
    - React-runtimeexecutor (= 0.64.3)
  - React-jsi (0.64.3):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-jsi/Default (= 0.64.3)
  - React-jsi/Default (0.64.3):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
  - React-jsiexecutor (0.64.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-perflogger (= 0.64.3)
  - React-jsinspector (0.64.3)
  - react-native-now-playing (0.2.1):
    - React-Core
  - react-native-safe-area-context (3.3.2):
    - React-Core
  - react-native-shazam-kit (0.1.3):
    - React-Core
  - React-perflogger (0.64.3)
  - React-RCTActionSheet (0.64.3):
    - React-Core/RCTActionSheetHeaders (= 0.64.3)
  - React-RCTAnimation (0.64.3):
    - FBReactNativeSpec (= 0.64.3)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.3)
    - React-Core/RCTAnimationHeaders (= 0.64.3)
    - React-jsi (= 0.64.3)
    - ReactCommon/turbomodule/core (= 0.64.3)
  - React-RCTBlob (0.64.3):
    - FBReactNativeSpec (= 0.64.3)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTBlobHeaders (= 0.64.3)
    - React-Core/RCTWebSocket (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-RCTNetwork (= 0.64.3)
    - ReactCommon/turbomodule/core (= 0.64.3)
  - React-RCTImage (0.64.3):
    - FBReactNativeSpec (= 0.64.3)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.3)
    - React-Core/RCTImageHeaders (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-RCTNetwork (= 0.64.3)
    - ReactCommon/turbomodule/core (= 0.64.3)
  - React-RCTLinking (0.64.3):
    - FBReactNativeSpec (= 0.64.3)
    - React-Core/RCTLinkingHeaders (= 0.64.3)
    - React-jsi (= 0.64.3)
    - ReactCommon/turbomodule/core (= 0.64.3)
  - React-RCTNetwork (0.64.3):
    - FBReactNativeSpec (= 0.64.3)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.3)
    - React-Core/RCTNetworkHeaders (= 0.64.3)
    - React-jsi (= 0.64.3)
    - ReactCommon/turbomodule/core (= 0.64.3)
  - React-RCTSettings (0.64.3):
    - FBReactNativeSpec (= 0.64.3)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.3)
    - React-Core/RCTSettingsHeaders (= 0.64.3)
    - React-jsi (= 0.64.3)
    - ReactCommon/turbomodule/core (= 0.64.3)
  - React-RCTText (0.64.3):
    - React-Core/RCTTextHeaders (= 0.64.3)
  - React-RCTVibration (0.64.3):
    - FBReactNativeSpec (= 0.64.3)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTVibrationHeaders (= 0.64.3)
    - React-jsi (= 0.64.3)
    - ReactCommon/turbomodule/core (= 0.64.3)
  - React-runtimeexecutor (0.64.3):
    - React-jsi (= 0.64.3)
  - ReactCommon/turbomodule/core (0.64.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.3)
    - React-Core (= 0.64.3)
    - React-cxxreact (= 0.64.3)
    - React-jsi (= 0.64.3)
    - React-perflogger (= 0.64.3)
  - RealmJS (10.12.0):
    - GCDWebServer
    - React
  - RNCAsyncStorage (1.17.3):
    - React-Core
  - RNCClipboard (1.8.5):
    - React-Core
  - RNScreens (3.10.2):
    - React-Core
    - React-RCTImage
  - RNShare (7.3.7):
    - React-Core
  - Yoga (1.14.0)

DEPENDENCIES:
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - "react-native-now-playing (from `../node_modules/@edualm/react-native-now-playing`)"
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-shazam-kit (from `../node_modules/@edualm/react-native-shazam-kit`)"
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - RealmJS (from `../node_modules/realm`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - boost-for-react-native
    - GCDWebServer

EXTERNAL SOURCES:
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  react-native-now-playing:
    :path: "../node_modules/@edualm/react-native-now-playing"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-shazam-kit:
    :path: "../node_modules/@edualm/react-native-shazam-kit"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RealmJS:
    :path: "../node_modules/realm"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost-for-react-native: 39c7adb57c4e60d6c5479dd8623128eb5b3f0f2c
  DoubleConversion: cf9b38bf0b2d048436d9a82ad2abe1404f11e7de
  FBLazyVector: c71c5917ec0ad2de41d5d06a5855f6d5eda06971
  FBReactNativeSpec: d0ba2f1498f90c671e7321111fa6ad70bb5bebe1
  GCDWebServer: 2c156a56c8226e2d5c0c3f208a3621ccffbe3ce4
  glog: 73c2498ac6884b13ede40eda8228cb1eee9d9d62
  RCT-Folly: ec7a233ccc97cc556cf7237f0db1ff65b986f27c
  RCTRequired: d34bf57e17cb6e3b2681f4809b13843c021feb6c
  RCTTypeSafety: 8dab4933124ed39bb0c1d88d74d61b1eb950f28f
  React: ef700aeb19afabff83a9cc5799ac955a9c6b5e0f
  React-callinvoker: 5547633d44f3e114b17c03c660ccb5faefd9ed2d
  React-Core: 0cebc45b3806ffad01abe3a427cbbca3fb7f29c5
  React-CoreModules: 29b3397adac0c04915cf93089328664868510717
  React-cxxreact: 61a28af12ba19749f6d3e3a7f14d5225c3fbe4f1
  React-jsi: a9b8de5c4e750cca487a1497421179b2cc965526
  React-jsiexecutor: 65e1b3ff67b87ec54e09ab3d06f2474e59ef3dcf
  React-jsinspector: 34e23860273a23695342f58eed3ffd3ba10c31e0
  react-native-now-playing: f9bcc1d8cab8a9e95b567641ba3d562dbe8bad23
  react-native-safe-area-context: 1c298d1cee1e333d3f44ea6dc8dfca086bd06242
  react-native-shazam-kit: 980a61f1d63b91518d6cf7febc5cd9a6506a14f7
  React-perflogger: cc76a4254d19640f1d8ad1c66fdee800414b805c
  React-RCTActionSheet: 7448f049318d8d7e8a9a1ebb742ada721757eea8
  React-RCTAnimation: fb9b3fa1a4a9f5e6ab01b3368693ce69860ba76a
  React-RCTBlob: a2e7056601c599c19884992f08ebacae810426f9
  React-RCTImage: 5a46c12327d0d6f6844a1fe38baa92a1e02847e8
  React-RCTLinking: 63dd8305591e1def35267557ed42918aec9eb30b
  React-RCTNetwork: d0516e39a5f736b2bff671c3e03804200161dcd3
  React-RCTSettings: a09566b14f1649f6c8a39ad1a174bb5c0631bb09
  React-RCTText: 04a2f0a281f715f0aed4f515717fafd64510e2c8
  React-RCTVibration: c7f845861e79eae13dc1e8217a3cf47a3945b504
  React-runtimeexecutor: 493d9abb8b23c3f84e19ae221eeba92cadcb70dc
  ReactCommon: 5d8dd4ab7e84638392cbd5db77c10e7a412bb69a
  RealmJS: 96d3c30fa8b8c9d3ec8403b7770ef2bf7e4f13f3
  RNCAsyncStorage: 5589f1e39b5f3a4f16a8cef46311852a8012633d
  RNCClipboard: fa2559eb7aa768c4c80c3dd62a0bd766f51dffdf
  RNScreens: 9c5c6b2ef47d0814e6d036339e4588eefad9ac54
  RNShare: d03cdc71e750246a48b81dcd62bd792bf57a758e
  Yoga: e6ecf3fa25af9d4c87e94ad7d5d292eedef49749

PODFILE CHECKSUM: 446d5336722600a4e150261d9c2601e6a5c52ab6

COCOAPODS: 1.16.2
