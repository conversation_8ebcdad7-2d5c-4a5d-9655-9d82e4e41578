# require File.join(File.dirname(`node --print 
# "require.resolve('expo/package.json')"`), "scripts/autolinking")
require File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "scripts/react_native_pods")
require File.join(File.dirname(`node --print "require.resolve('@react-native-community/cli-platform-ios/package.json')"`), "native_modules")

platform :ios, '12.0'

require 'json'
podfile_properties = JSON.parse(File.read('./Podfile.properties.json')) rescue {}

target 'SongButlerRN' do
  # use_expo_modules!
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => podfile_properties['expo.jsEngine'] == 'hermes'
  )

  # Uncomment to opt-in to using Flipper
  #
  # if !ENV['CI']
  #   use_flipper!('Flipper' => '0.75.1', 'Flipper-Folly' => '2.5.3', 'Flipper-RSocket' => '1.3.1')
  # end

  post_install do |installer|
    react_native_post_install(installer)

    # Workaround `Cycle inside FBReactNativeSpec` error for react-native 0.64
    # Reference: https://github.com/software-mansion/react-native-screens/issues/842#issuecomment-812543933
    installer.pods_project.targets.each do |target|
      if (target.name&.eql?('FBReactNativeSpec'))
        target.build_phases.each do |build_phase|
          if (build_phase.respond_to?(:name) && build_phase.name.eql?('[CP-User] Generate Specs'))
            target.build_phases.move(build_phase, 0)
          end
        end
      end
    end

    targetsToFix = ["React-Core-AccessibilityResources", "EXConstants-EXConstants"];
    installer.target_installation_results.pod_target_installation_results
      .each do |pod_name, target_installation_result|
      target_installation_result.resource_bundle_targets.each do |resource_bundle_target|
        if targetsToFix.include? resource_bundle_target.name
          resource_bundle_target.build_configurations.each do |config|
            config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
          end
        end
      end
    end
  end

  post_integrate do |installer|
    begin
      expo_patch_react_imports!(installer)
    rescue => e
      Pod::UI.warn e
    end
  end
end
