/**
 * Learn more about using TypeScript with React Navigation:
 * https://reactnavigation.org/docs/typescript/
 */

import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, NavigatorScreenParams } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ImageSourcePropType, StyleProp, ViewStyle } from 'react-native';

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}

export type RootStackParamList = {
  Root: NavigatorScreenParams<RootTabParamList> | undefined;
  Search: undefined;
  InfoModal: undefined;
  SongDetail: undefined;
  History: undefined;
};

export type RootStackScreenProps<Screen extends keyof RootStackParamList> = NativeStackScreenProps<
  RootStackParamList,
  Screen
>;

export type RootTabParamList = {
  SearchTab: undefined;
  HistoryTab: undefined;
};

export type RootTabScreenProps<Screen extends keyof RootTabParamList> = CompositeScreenProps<
  BottomTabScreenProps<RootTabParamList, Screen>,
  NativeStackScreenProps<RootStackParamList>
>;

export type APISongDetailAttributes = {
 album: string;
 album_art_url: string;
 artist: string;
 title: string;
}

export type APISongDetailLinks = {
  apple_music: string;
  spotify: string;
  youtube: string;
}

export type APISongResponse = {
  attributes: APISongDetailAttributes;
  links: APISongDetailLinks;
}

export interface SongLinks {
  appleMusic: string | undefined
  spotify: string | undefined
  youtube: string | undefined
}

export interface Song {
  links: SongLinks

  album: string | undefined
  albumArtURL: string | undefined
  artist: string | undefined
  queryDates: number[]
  lastQueryDate: number | undefined
  title: string | undefined
}

export interface OldHistoryEntry {
  entry: APISongResponse
  date: number
}

export interface MusicServiceRowProps {
  musicServiceName: string
  musicServiceImageSource: ImageSourcePropType
  onPress: (share: boolean) => boolean,
  style: StyleProp<ViewStyle>
}